/* Woo2Shopify Admin Styles */

.woo2shopify-dashboard {
    margin-top: 20px;
}

.woo2shopify-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.woo2shopify-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.woo2shopify-card h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
}

.woo2shopify-stat .number {
    display: block;
    font-size: 32px;
    font-weight: 600;
    color: #0073aa;
    line-height: 1;
}

.woo2shopify-stat .label {
    display: block;
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

.woo2shopify-connection-status {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

#connection-result {
    padding: 10px;
    border-radius: 4px;
    display: none;
}

#connection-result.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

#connection-result.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.woo2shopify-migration-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.woo2shopify-migration-section h2 {
    margin: 0 0 15px 0;
    font-size: 20px;
    font-weight: 600;
}

.woo2shopify-migration-options {
    margin: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.woo2shopify-migration-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.woo2shopify-migration-controls {
    margin: 20px 0;
}

.woo2shopify-progress-bar {
    position: relative;
    background: #f1f1f1;
    border-radius: 10px;
    height: 20px;
    margin: 15px 0;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(90deg, #0073aa, #005a87);
    height: 100%;
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 10px;
    position: relative;
}

.progress-fill.progress-active {
    background: linear-gradient(90deg, #0073aa, #005a87, #0073aa);
    background-size: 200% 100%;
    animation: progressShimmer 2s linear infinite;
}

@keyframes progressShimmer {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
    font-weight: 600;
    color: #23282d;
}

.woo2shopify-progress-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 14px;
}

.progress-stats {
    font-weight: 600;
    color: #0073aa;
}

.progress-breakdown {
    font-size: 12px;
    color: #666;
    margin-left: 10px;
}

.progress-breakdown #successful-count {
    color: #46b450;
    font-weight: 600;
}

.progress-breakdown #failed-count {
    color: #dc3232;
    font-weight: 600;
}

.progress-status {
    color: #666;
    font-style: italic;
}

.woo2shopify-results-summary {
    display: flex;
    gap: 30px;
    margin-top: 15px;
}

.result-item {
    text-align: center;
    padding: 15px;
    border-radius: 4px;
    min-width: 100px;
}

.result-item.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
}

.result-item.failed {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
}

.result-item .count {
    display: block;
    font-size: 24px;
    font-weight: 600;
    line-height: 1;
}

.result-item.success .count {
    color: #155724;
}

.result-item.failed .count {
    color: #721c24;
}

.result-item .label {
    display: block;
    font-size: 12px;
    margin-top: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Settings styles */
.woo2shopify-range {
    width: 200px;
}

.range-value {
    margin-left: 10px;
    font-weight: 600;
    color: #0073aa;
}

/* Logs styles */
.woo2shopify-logs {
    margin-top: 20px;
}

.status-success {
    color: #155724;
    background: #d4edda;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
}

.status-failed,
.status-error {
    color: #721c24;
    background: #f8d7da;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
}

.status-pending,
.status-processing {
    color: #856404;
    background: #fff3cd;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
}

/* Help styles */
.woo2shopify-help {
    margin-top: 20px;
    max-width: 800px;
}

.woo2shopify-help h2 {
    color: #23282d;
    border-bottom: 1px solid #ccd0d4;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.woo2shopify-help ol {
    margin-left: 20px;
}

.woo2shopify-help li {
    margin-bottom: 10px;
    line-height: 1.6;
}

.woo2shopify-faq h3 {
    color: #0073aa;
    margin-top: 25px;
    margin-bottom: 10px;
}

.woo2shopify-faq p {
    margin-bottom: 15px;
    line-height: 1.6;
}

/* Responsive design */
@media (max-width: 768px) {
    .woo2shopify-cards {
        grid-template-columns: 1fr;
    }
    
    .woo2shopify-progress-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .woo2shopify-results-summary {
        flex-direction: column;
        gap: 15px;
    }
    
    .result-item {
        text-align: left;
    }
}

/* Loading animation */
.woo2shopify-loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button states */
.button.loading {
    position: relative;
    color: transparent !important;
}

.button.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid #fff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Tab content */
.tab-content {
    margin-top: 20px;
}

/* Form improvements */
.form-table th {
    width: 200px;
    padding: 20px 10px 20px 0;
}

.form-table td {
    padding: 15px 10px;
}

.form-table input[type="url"],
.form-table input[type="password"],
.form-table input[type="number"] {
    width: 100%;
    max-width: 400px;
}

.form-table .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
}

/* Language and Currency Tags */
.language-list, .currency-list {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.language-tag, .currency-tag {
    display: inline-block;
    background: #0073aa;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.currency-tag {
    background: #28a745;
}

/* Multi-language and currency info */
.woo2shopify-multilang-info {
    background: #e7f3ff;
    border: 1px solid #b3d9ff;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
}

.woo2shopify-multilang-info h4 {
    margin: 0 0 10px 0;
    color: #0073aa;
}

.woo2shopify-multilang-info p {
    margin: 5px 0;
    font-size: 14px;
}

.woo2shopify-multilang-info .notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 10px;
    border-radius: 3px;
    margin-top: 10px;
}

/* Notification styles */
.woo2shopify-notice {
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid;
    border-radius: 0 4px 4px 0;
}

.woo2shopify-notice.success {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.woo2shopify-notice.error {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.woo2shopify-notice.warning {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.woo2shopify-notice.info {
    background: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* Video Statistics Styles */
.video-stats {
    display: flex;
    gap: 20px;
    margin: 15px 0;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    min-width: 80px;
}

.stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #0073aa;
    line-height: 1;
}

.stat-label {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    text-align: center;
}

.button-small {
    padding: 4px 8px !important;
    font-size: 12px !important;
    height: auto !important;
    line-height: 1.4 !important;
}

/* Language and Currency Tags */
.language-list,
.currency-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.language-tag,
.currency-tag {
    display: inline-block;
    padding: 4px 8px;
    background: #e1f5fe;
    color: #01579b;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.currency-tag {
    background: #f3e5f5;
    color: #4a148c;
}

/* Video Cache Card Specific Styles */
.woo2shopify-card .video-stats .stat-item {
    background: #fff;
    border: 1px solid #e0e0e0;
}

.woo2shopify-card .video-stats .stat-item:first-child .stat-number {
    color: #28a745;
}

.woo2shopify-card .video-stats .stat-item:last-child .stat-number {
    color: #ffc107;
}

/* Selective Migration Styles */
.woo2shopify-selective-migration,
.woo2shopify-page-migration {
    max-width: 1200px;
}

.woo2shopify-filters {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #e0e0e0;
}

.woo2shopify-filters h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: flex-end;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.filter-row:last-child {
    margin-bottom: 0;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #555;
    font-size: 13px;
}

.filter-group input,
.filter-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.filter-group input:focus,
.filter-group select:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

#product-search {
    min-width: 250px;
}

#clear-search {
    margin-left: 5px;
}

.filter-results-count {
    color: #666;
    font-style: italic;
    margin-left: 10px;
    align-self: center;
}

/* Migration Options */
.woo2shopify-migration-options {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    border: 1px solid #e0e0e0;
}

.woo2shopify-migration-options h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
}

.options-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.options-group:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.options-group h4 {
    margin: 0 0 15px 0;
    color: #555;
    font-size: 14px;
    font-weight: 600;
}

.options-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: normal;
}

.options-group input[type="checkbox"] {
    margin-right: 8px;
}

.language-options,
.currency-options {
    margin-top: 10px;
    padding-left: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.language-option,
.currency-option {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.language-option input,
.currency-option input {
    margin-right: 5px;
}

/* Product Selection Styles */
.woo2shopify-product-selection {
    margin: 20px 0;
}

.selection-controls {
    background: #fff;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    border: 1px solid #e0e0e0;
    border-bottom: none;
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

#selected-count {
    color: #666;
    font-weight: 600;
    margin-left: auto;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    padding: 20px;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 0 0 8px 8px;
    max-height: 600px;
    overflow-y: auto;
}

.product-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.2s ease;
    background: #fff;
}

.product-item:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
}

.product-item.migrated {
    background: #f8f9fa;
    border-color: #28a745;
}

.product-item.migrated .product-label {
    opacity: 0.7;
}

.product-label {
    display: block;
    padding: 15px;
    cursor: pointer;
    height: 100%;
}

.product-info {
    display: flex;
    gap: 15px;
}

.product-image {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 4px;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    color: #999;
    font-size: 24px;
}

.product-details {
    flex: 1;
    min-width: 0;
}

.product-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 10px;
}

.product-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
    flex: 1;
    margin-right: 10px;
}

.product-type-badge {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
    text-transform: uppercase;
    flex-shrink: 0;
}

.product-type-simple {
    background: #e3f2fd;
    color: #1976d2;
}

.product-type-variable {
    background: #fff3e0;
    color: #f57c00;
}

.product-type-grouped {
    background: #f3e5f5;
    color: #7b1fa2;
}

.product-type-external {
    background: #e8f5e8;
    color: #388e3c;
}

.product-meta {
    margin-bottom: 10px;
}

.meta-row {
    display: flex;
    margin-bottom: 5px;
    font-size: 13px;
}

.meta-label {
    font-weight: 600;
    color: #666;
    min-width: 60px;
}

.meta-value {
    color: #333;
}

.price-regular {
    text-decoration: line-through;
    color: #999;
    margin-right: 5px;
}

.price-sale {
    color: #d32f2f;
    font-weight: 600;
}

.price {
    color: #333;
    font-weight: 600;
}

.price-na {
    color: #999;
    font-style: italic;
}

.variation-count {
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

.language-tags {
    margin-bottom: 10px;
}

.product-status {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: #666;
}

.product-status .dashicons {
    font-size: 16px;
}

.product-status .dashicons-yes-alt {
    color: #28a745;
}

.product-status .dashicons-migrate {
    color: #0073aa;
}

.loading-indicator,
.no-products-found,
.error-message {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.loading-indicator .spinner {
    float: none;
    margin: 0 10px 0 0;
}

.pagination-controls {
    text-align: center;
    padding: 20px;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-top: none;
    border-radius: 0 0 8px 8px;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-row input,
.filter-row select {
    min-width: 200px;
}

.woo2shopify-migration-options {
    background: #fff;
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
}

.woo2shopify-migration-options label {
    display: inline-block;
    margin-right: 20px;
    margin-bottom: 10px;
}

.selection-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.selection-controls span {
    margin-left: auto;
    font-weight: 600;
    color: #0073aa;
}

/* Product Grid */
.products-grid,
.pages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.product-item,
.page-item {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background: #fff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.product-item:hover,
.page-item:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
}

.product-item.migrated,
.page-item.migrated {
    background: #f0f8f0;
    border-color: #28a745;
    opacity: 0.7;
}

.product-item label,
.page-item label {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    cursor: pointer;
    margin: 0;
}

.product-item input[type="checkbox"],
.page-item input[type="checkbox"] {
    margin: 5px 0 0 0;
    flex-shrink: 0;
}

.product-info,
.page-info {
    display: flex;
    gap: 15px;
    width: 100%;
}

.product-image {
    width: 60px;
    height: 60px;
    border-radius: 4px;
    overflow: hidden;
    flex-shrink: 0;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    font-size: 12px;
    color: #666;
    text-align: center;
}

.product-details,
.page-info {
    flex: 1;
}

.product-details h4,
.page-info h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.product-details p,
.page-info p {
    margin: 4px 0;
    font-size: 13px;
    color: #666;
}

/* Pagination */
.pagination-controls {
    text-align: center;
    margin: 30px 0;
}

/* Progress Bar */
.woo2shopify-progress {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin: 15px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
}

.progress-text {
    font-weight: 600;
    color: #0073aa;
}

.progress-details {
    color: #666;
}

/* Results */
.woo2shopify-results {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.results-summary {
    margin-bottom: 20px;
}

.results-details {
    max-height: 300px;
    overflow-y: auto;
    border-top: 1px solid #eee;
    padding-top: 15px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-row input,
    .filter-row select {
        min-width: auto;
        width: 100%;
    }

    .selection-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .selection-controls span {
        margin-left: 0;
        text-align: center;
    }

    .products-grid,
    .pages-grid {
        grid-template-columns: 1fr;
    }

    .product-info {
        flex-direction: column;
        gap: 10px;
    }

    .product-image {
        align-self: flex-start;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Video Migration Styles */
.woo2shopify-video-migration {
    max-width: 1000px;
}

.video-selection-controls {
    margin: 15px 0;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
}

.video-selection-controls .button {
    margin-right: 10px;
}

.video-list {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background: #fff;
}

.video-product-group {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.video-product-group:last-child {
    border-bottom: none;
}

.video-product-group h4 {
    margin: 0 0 15px 0;
    color: #23282d;
    font-size: 16px;
}

.video-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    margin-bottom: 10px;
    background: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #e5e5e5;
}

.video-item label {
    display: flex;
    align-items: center;
    flex: 1;
    cursor: pointer;
}

.video-item input[type="checkbox"] {
    margin-right: 10px;
}

.video-info {
    flex: 1;
}

.video-info strong {
    display: block;
    margin-bottom: 5px;
    color: #23282d;
}

.video-info small {
    display: block;
    color: #666;
    margin-bottom: 2px;
}

.video-status {
    display: flex;
    align-items: center;
    min-width: 150px;
    text-align: right;
}

.video-status .status-indicator {
    font-size: 16px;
    margin-right: 8px;
}

.video-status .status-text {
    font-size: 12px;
    color: #666;
}

.video-migration-actions {
    margin-top: 20px;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
    text-align: center;
}

.video-migration-progress {
    margin-top: 15px;
}

.video-status-warning {
    margin: 10px 0;
    padding: 10px;
    border-radius: 4px;
}

.video-status-warning strong {
    display: block;
}
